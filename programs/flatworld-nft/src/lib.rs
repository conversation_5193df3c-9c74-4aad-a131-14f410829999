use anchor_lang::prelude::*;
use anchor_spl::token::{self, Token, TokenAccount, Mint, MintTo};

declare_id!("F1aTWbURnE2C3v5g3F5c9b5v9c1bZc9b5v9c1bZc9b5v9c1");

#[program]
pub mod flatworld_nft {
    use super::*;

    #[derive(Accounts)]
    pub struct InitializeGlobalState<'info> {
        #[account(init, payer = authority, space = 8 + GlobalState::LEN)]
        pub global_state: Account<'info, GlobalState>,
        #[account(mut)]
        pub authority: Signer<'info>,
        pub system_program: Program<'info, System>,
    }

    pub fn initialize_global_state(ctx: Context<InitializeGlobalState>) -> Result<()> {
        ctx.accounts.global_state.authority = ctx.accounts.authority.key();
        ctx.accounts.global_state.minted_count = 0;
        ctx.accounts.global_state.max_supply = 999; // 改为999总量
        ctx.accounts.global_state.mint_price = 100_000_000; // 0.1 SOL (100,000,000 lamports)
        Ok(())
    }

    #[derive(Accounts)]
    pub struct MintNft<'info> {
        #[account(mut)]
        pub global_state: Account<'info, GlobalState>,
        #[account(mut)]
        pub payer: Signer<'info>, // 支付0.1 SOL的用户
        #[account(
            mut,
            constraint = payer.key() != recipient.key()
        )]
        pub recipient: AccountInfo<'info>, // NFT接收者
        #[account(mut)]
        pub mint: Account<'info, Mint>,
        #[account(
            init,
            payer = payer,
            token::mint = mint,
            token::authority = recipient,
        )]
        pub token_account: Account<'info, TokenAccount>,
        #[account(mut)]
        pub treasury: AccountInfo<'info>, // 接收支付的钱包
        pub token_program: Program<'info, Token>,
        pub system_program: Program<'info, System>,
        pub rent: Sysvar<'info, Rent>,
    }

    pub fn mint_nft(ctx: Context<MintNft>, character_id: String) -> Result<()> {
        // 检查支付
        require!(
            ctx.accounts.payer.lamports() >= ctx.accounts.global_state.mint_price,
            FlatworldError::InsufficientFunds
        );
        
        // 转账0.1 SOL到treasury
        **ctx.accounts.treasury.lamports.borrow_mut() = ctx.accounts.treasury.lamports()
            .checked_add(ctx.accounts.global_state.mint_price)
            .unwrap();
        **ctx.accounts.payer.lamports.borrow_mut() = ctx.accounts.payer.lamports()
            .checked_sub(ctx.accounts.global_state.mint_price)
            .unwrap();

        // 检查供应量
        require!(
            ctx.accounts.global_state.minted_count < ctx.accounts.global_state.max_supply,
            FlatworldError::MaxSupplyReached
        );

        // 铸造代币
        let cpi_accounts = MintTo {
            mint: ctx.accounts.mint.to_account_info(),
            to: ctx.accounts.token_account.to_account_info(),
            authority: ctx.accounts.payer.to_account_info(),
        };
        let cpi_program = ctx.accounts.token_program.to_account_info();
        let cpi_ctx = CpiContext::new(cpi_program, cpi_accounts);
        token::mint_to(cpi_ctx, 1)?;

        // 生成固定元数据URI
        let metadata_uri = format!("https://ipfs.io/hash/{}.json", character_id);

        // 更新全局状态
        ctx.accounts.global_state.minted_count += 1;

        // 发出事件
        emit!(NftMinted {
            recipient: ctx.accounts.recipient.key(),
            token_id: ctx.accounts.global_state.minted_count,
            metadata_uri: metadata_uri.clone()
        });

        Ok(())
    }
}

#[account]
pub struct GlobalState {
    pub authority: Pubkey,      // 管理员地址
    pub minted_count: u64,      // 已铸造数量
    pub max_supply: u64,        // 最大供应量 (999)
    pub mint_price: u64,        // 铸造价格 (lamports)
}

#[event]
pub struct NftMinted {
    pub recipient: Pubkey,      // NFT接收者
    pub token_id: u64,          // NFT ID
    pub metadata_uri: String,   // 元数据URI
}

#[error_code]
pub enum FlatworldError {
    #[msg("Maximum NFT supply reached")]
    MaxSupplyReached,
    #[msg("Insufficient funds for minting")]
    InsufficientFunds,
}
