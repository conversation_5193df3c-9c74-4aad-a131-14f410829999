[features]
seeds = false
skip-lint = false

[programs.localnet]
cfx_stake_core = "C6S9i5vFWTcVymiBrmUrCpZGdSyChWPrAbfFd9fdRs2r"
cfx_token = "H7m4Y4ZXnXPEoKsBGyestPDdwvPrzqYTBVY8sXbjCgHr"

[programs.devnet]
cfx_stake_core = "HupexUsRkmBGiFxSM14JwUJs7ADJNFfQ6UygRuKrHyp8"
cfx_token = "AfMqiffUwwkSNLS7tpMhyCRm28qqqMGCfYKCBkZo6uHM"

[registry]
url = "https://api.apr.dev"

[provider]
cluster = "devnet"
wallet = "/Users/<USER>/.config/solana/devnet.json"

[test]
startup_wait = 10000
skip_local_validator = true

[scripts]
test = "yarn run mocha -t 1000000 tests/**/*.js"

# BPF loader configuration for larger programs
[test.validator]
# Use the upgraded BPF loader that supports larger programs
[[test.validator.clone]]
address = "BPFLoaderUpgradeab1e11111111111111111111111"

# Increase the compute budget for the program
[test.validator.compute_budget]
compute_unit_limit = 1400000
heap_size = 256000
