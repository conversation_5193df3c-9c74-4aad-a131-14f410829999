{"timestamp": "2025-05-31T03:02:33.740Z", "network": "devnet", "deployer": "4o3E6G5wTwcqLR2J278h27pGQmU6YesSNKSjUdQbhFK9", "programs": {"CFX_STAKE_CORE": "HupexUsRkmBGiFxSM14JwUJs7ADJNFfQ6UygRuKrHyp8"}, "tokens": {"CFX_TOKEN_MINT": "AfMqiffUwwkSNLS7tpMhyCRm28qqqMGCfYKCBkZo6uHM"}, "deployed_accounts": {"stake_pool": {"address": "43yd7mve4YH3U1dDyfuCks27EcC7EdT9vvXDJ837i1FD", "type": "PDA", "program": "CFX_STAKE_CORE", "description": "质押池账户", "seeds": ["stake_pool", "CFX_TOKEN_MINT"], "bump": 255}, "authority_wallet": {"address": "4o3E6G5wTwcqLR2J278h27pGQmU6YesSNKSjUdQbhFK9", "type": "Wallet", "description": "当前部署者和管理员钱包", "role": "admin"}, "team_wallet": {"address": "********************************************", "type": "Wallet", "description": "团队钱包地址，用于接收流动性资金", "role": "team_treasury"}, "token_vault": {"address": "GHjch8hUBategenAJwB41Mned3mkpr69r82kx87CD7hv", "type": "<PERSON><PERSON><PERSON><PERSON>unt", "program": "TOKEN_PROGRAM", "description": "质押池代币金库", "mint": "AfMqiffUwwkSNLS7tpMhyCRm28qqqMGCfYKCBkZo6uHM", "authority": "43yd7mve4YH3U1dDyfuCks27EcC7EdT9vvXDJ837i1FD"}, "multisig_config": {"address": "DdTsEKJwry1WziKFTgVK3fSYMSY9vqcE2iZTZkE3eCLt", "type": "PDA", "program": "CFX_STAKE_CORE", "description": "多签配置账户", "seeds": ["multisig_config", "stake_pool"], "bump": 255, "signers": ["DJmqhERPWgaRfN3FPCJRtz3hARN8EXHUtn9ppjK2Hn6o", "Fs11duup39VxvzsscgCiRQJa82k39sNw3Hu51KztBbUs", "BByuTQZzoUhTAtxBZtmNsT6bUK3g4MGvXy7tWQmqBRoK"], "threshold": 2, "proposal_count": 0, "created_at": "2025-05-31T03:30:00.000Z", "transaction_signature": "FwVW1gGNxMwNCgq8rq4J4DMsJEdARFs8cRLRynpaaqEghMGaF4k2m53Jmz3PunBzxNMP7GFeHV4jcz8ZHnJzarj"}}, "deployed_programs": [{"name": "cfx-stake-core", "id": "HupexUsRkmBGiFxSM14JwUJs7ADJNFfQ6UygRuKrHyp8", "dirName": "cfx-stake-core"}], "metadata": {"total_programs": 1, "successful_deployments": 1, "deployment_tool": "anchor", "version": "1.0.0", "description": "Chain-Fox DAO - Solana 质押协议", "cfx_token_info": {"name": "Chain-Fox Token", "symbol": "CFX", "decimals": 6, "total_supply": "1000000000000000", "network": "devnet", "created_at": "2025-05-31T03:00:43.873Z"}, "last_regeneration": "2025-05-31T02:51:49.108Z", "regeneration_reason": "Program code changes"}}